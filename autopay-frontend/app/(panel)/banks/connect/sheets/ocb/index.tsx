import { BankAccountForm } from '@/app/(panel)/banks/common/components/BankAccountForm'
import BankIntroduction from '@/app/(panel)/banks/connect/components/BankIntroduction'
import type { BankConfig } from '@/lib/types/banks'
import { queryFetchHelper } from '@/lib/utils/fetchHelper'
import { useMutation } from '@tanstack/react-query'
import { toast } from 'sonner'
import BankNote from '../../components/BankNote'
import { useStore } from '../../stores/store'
import CreateVa from './CreateVa'
import Greeting from './Greeting'
import Otp from './Otp'

export default function Index({ bankCode, bank }: { bankCode: string; bank: BankConfig | null }) {
  const { setIsShowCreateVaForm, setBankAccount } = useStore()

  const {
    isPending,
    mutate: checkBankAccount,
    isError: isErrorMutation,
    error,
    reset: resetMutationState,
  } = useMutation({
    mutationFn: (data: Record<string, string>) =>
      queryFetchHelper(process.env.NEXT_PUBLIC_API_URL + `/banks/check-account`, {
        method: 'POST',
        body: JSON.stringify(data),
      }),
    onError: (error) => {
      // toast.error(error.message, {
      //   style: {
      //     width: 'fit-content',
      //     whiteSpace: 'nowrap',
      //   },
      // });
    },
    onSuccess: (data) => {
      if (data.success) {
        toast.success(data.message ?? 'Kết nối thành công')
        setBankAccount(data)
        setIsShowCreateVaForm(true)
      } else {
        toast.error(data.message)
      }
    },
  })

  const handleFormSubmit = (formData: Record<string, string>) => {
    checkBankAccount(formData)
  }

  return (
    <>
      <BankIntroduction bank={bank} />
      <div className="text-center">Vui lòng nhập thông tin kết nối để tiếp tục</div>
      <BankAccountForm
        bankCode={bankCode}
        bank={bank}
        onSubmit={handleFormSubmit}
        isPending={isPending}
        isError={isErrorMutation}
        error={error}
        resetError={resetMutationState}
        enableAccountLookup={true}
      />
      <BankNote bank={bank} />
      <CreateVa
        bankCode={bankCode}
        bank={bank}
      />
      <Otp
        bankCode={bankCode}
        bank={bank}
      />
      <Greeting
        bankCode={bankCode}
        bank={bank}
      />
    </>
  )
}
