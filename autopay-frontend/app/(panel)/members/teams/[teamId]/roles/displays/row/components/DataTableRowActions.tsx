import { Button } from '@/components/ui/button'
import { DotsHorizontalIcon } from '@radix-ui/react-icons'
import { Row } from '@tanstack/react-table'

import { Actions } from '../../../components/Actions'

interface DataTableRowActionsProps<TData> {
  row: Row<TData>
}

export function DataTableRowActions({ row }: DataTableRowActionsProps<Role>) {
  const data = row.original

  return (
    <Actions
      data={data}
      triggerChildren={
        <Button
          variant="ghost"
          className="data-[state=open]:bg-muted flex h-8 w-8 p-0">
          <DotsHorizontalIcon className="h-4 w-4" />
          <span className="sr-only">Open menu</span>
        </Button>
      }
    />
  )
}
