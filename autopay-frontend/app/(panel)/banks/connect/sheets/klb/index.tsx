import { BankAccountForm } from '@/app/(panel)/banks/common/components/BankAccountForm'
import type { BankConfig } from '@/lib/types/banks'
import { queryFetchHelper } from '@/lib/utils/fetchHelper'
import { useMutation } from '@tanstack/react-query'
import { toast } from 'sonner'
import BankIntroduction from '../../components/BankIntroduction'
import BankNote from '../../components/BankNote'

export default function Index({ bankCode, bank }: { bankCode: string; bank: BankConfig | null }) {
  const {
    isPending,
    mutate,
    isError,
    error,
    reset: resetMutationState,
  } = useMutation({
    mutationFn: (data: Record<string, string>) =>
      queryFetchHelper(process.env.NEXT_PUBLIC_API_URL + `/banks/${bankCode}/check-account`, {
        method: 'POST',
        body: JSON.stringify(data),
      }),
    onError: (error) => {
      toast.error(error.message)
    },
    onSuccess: (data) => {
      if (data.success) {
        toast.success('<PERSON>ết nối thành công')
        // setIsShowRegisterForm(false);
      } else {
        toast.error(data.message)
      }
    },
  })

  const handleFormSubmit = (formData: Record<string, string>) => {
    mutate(formData)
  }

  return (
    <>
      <BankIntroduction bank={bank} />
      <div className="text-center">Vui lòng nhập thông tin kết nối để tiếp tục</div>
      <BankAccountForm
        bankCode={bankCode}
        bank={bank}
        onSubmit={handleFormSubmit}
        isPending={isPending}
        isError={isError}
        error={error}
        resetError={resetMutationState}
        enableAccountLookup={true}
      />
      <BankNote bank={bank} />
    </>
  )
}
