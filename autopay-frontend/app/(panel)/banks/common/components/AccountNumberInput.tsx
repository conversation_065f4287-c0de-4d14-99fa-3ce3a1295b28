'use client'

import { FormControl, FormDescription, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { cn } from '@/lib/utils'
import { ControllerRenderProps, FieldPath, FieldValues } from 'react-hook-form'
import { useAccountNameLookup } from '../hooks/use-account-name-lookup'
import { Icons } from '@/components/Icons'

interface AccountNumberInputProps<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
> {
  field: ControllerRenderProps<TFieldValues, TName>
  bankCode: string
  title?: string
  description?: string
  placeholder?: string
  className?: string
  disabled?: boolean
  enableLookup?: boolean
}

/**
 * Account number input component with automatic account name lookup
 * Displays account holder name when a valid account number is entered
 */
export function AccountNumberInput<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
>({
  field,
  bankCode,
  title = 'Số tài khoản',
  description = 'Số tài khoản của bạn tại ngân hàng',
  placeholder = 'Nhập số tài khoản',
  className,
  disabled = false,
  enableLookup = true,
}: AccountNumberInputProps<TFieldValues, TName>): JSX.Element {
  const { accountName, isLookingUp, error } = useAccountNameLookup({
    bankCode,
    accountNumber: field.value as string,
    enabled: enableLookup && !disabled,
  })

  return (
    <FormItem>
      <FormLabel>{title}</FormLabel>
      <FormControl>
        <div className="relative">
          <Input
            {...field}
            value={(field.value as string) || ''}
            placeholder={placeholder}
            disabled={disabled}
            className={cn(className)}
          />
          {enableLookup && isLookingUp && (
            <div className="absolute right-3 top-1/2 -translate-y-1/2">
              <Icons.spinner className="h-4 w-4 animate-spin text-muted-foreground" />
            </div>
          )}
        </div>
      </FormControl>
      <FormDescription>{description}</FormDescription>
      
      {/* Display account name if lookup is successful */}
      {enableLookup && accountName && !isLookingUp && (
        <div className="text-sm text-green-600 dark:text-green-400 font-medium">
          ✓ Chủ tài khoản: {accountName}
        </div>
      )}
      
      {/* Display lookup error if any */}
      {enableLookup && error && !isLookingUp && (
        <div className="text-sm text-orange-600 dark:text-orange-400">
          ⚠ {error}
        </div>
      )}
      
      <FormMessage />
    </FormItem>
  )
}
