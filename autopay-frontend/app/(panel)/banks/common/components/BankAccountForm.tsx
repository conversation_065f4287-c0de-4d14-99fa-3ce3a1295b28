'use client'

import * as arrowData from '@/assets/lottie/arrow-right.json'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Icons } from '@/components/Icons'
import { zodResolver } from '@hookform/resolvers/zod'
import dynamic from 'next/dynamic'
import { Popover as PopoverRadix } from 'radix-ui'
import { useForm } from 'react-hook-form'
import { MdError } from 'react-icons/md'
import { z } from 'zod'
import { RefObject, useMemo, useRef } from 'react'
import { useOnClickOutside } from 'usehooks-ts'
import type { BankConfig, BankConnectionField } from '@/lib/types/banks'
import { createDefaultValues, createDynamicSchema } from '@/app/(panel)/banks/connect/common/helper'
import { AccountNumberInput } from './AccountNumberInput'

const Lottie = dynamic(() => import('react-lottie-player'), { ssr: false })

interface BankAccountFormProps {
  bankCode: string
  bank: BankConfig | null
  onSubmit: (data: Record<string, string>) => void
  isPending?: boolean
  isError?: boolean
  error?: Error | null
  resetError?: () => void
  submitButtonText?: string
  enableAccountLookup?: boolean
}

/**
 * Reusable bank account form component with dynamic field generation
 * Supports account name lookup for account number fields
 */
export function BankAccountForm({
  bankCode,
  bank,
  onSubmit,
  isPending = false,
  isError = false,
  error = null,
  resetError,
  submitButtonText = 'Tiếp tục',
  enableAccountLookup = true,
}: BankAccountFormProps): JSX.Element {
  const bankRequiredFields = useMemo<BankConnectionField[]>(() => {
    if (!bank) return []

    // Map connection_fields to BankConnectionField format
    const fieldMappings: Record<string, BankConnectionField> = {
      accountNumber: {
        name: 'accountNumber',
        title: 'Số tài khoản',
        description: 'Số tài khoản của bạn tại ngân hàng',
        min_length: 3,
      },
      idCardNumber: {
        name: 'idCardNumber',
        title: 'Số CMND/CCCD',
        description: 'Số chứng minh nhân dân hoặc căn cước công dân',
        min_length: 9,
      },
      phoneNumber: {
        name: 'phoneNumber',
        title: 'Số điện thoại',
        description: 'Số điện thoại đăng ký với ngân hàng',
        min_length: 10,
      },
    }

    return bank.connection_fields.map((fieldName: string) => fieldMappings[fieldName]).filter(Boolean)
  }, [bank])

  const dynamicSchema = useMemo(() => createDynamicSchema(bankRequiredFields), [bankRequiredFields])

  const form = useForm<z.infer<typeof dynamicSchema>>({
    resolver: zodResolver(dynamicSchema),
    defaultValues: createDefaultValues(bankRequiredFields),
  })

  const handleSubmit = (values: z.infer<typeof dynamicSchema>) => {
    const formData = {
      ...values,
      bankCode,
    }
    onSubmit(formData)
  }

  const refPopover = useRef<HTMLDivElement>(null)

  useOnClickOutside(refPopover as RefObject<HTMLElement>, () => {
    if (resetError) {
      resetError()
    }
  })

  return (
    <Card className="border-0">
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(handleSubmit)}
          className="**:data-[error=true]:text-primary space-y-8 p-4"
        >
          {bankRequiredFields &&
            bankRequiredFields.length > 0 &&
            bankRequiredFields.map((bankField) => (
              <FormField
                key={bankField.name}
                control={form.control}
                name={bankField.name}
                render={({ field }) => {
                  // Use AccountNumberInput for account number fields
                  if (bankField.name === 'accountNumber') {
                    return (
                      <AccountNumberInput
                        field={field}
                        bankCode={bankCode}
                        title={bankField.title}
                        description={bankField.description}
                        enableLookup={enableAccountLookup}
                      />
                    )
                  }

                  // Use regular input for other fields
                  return (
                    <FormItem>
                      <FormLabel>{bankField.title}</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          value={(field.value as string) || ''}
                        />
                      </FormControl>
                      <FormDescription>{bankField.description}</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )
                }}
              />
            ))}
          
          <Popover open={isError}>
            <PopoverTrigger asChild>
              <Button
                type="submit"
                size="sm"
                className="w-full gap-2"
                disabled={isPending}
              >
                {isPending && <Icons.spinner className="animate-spin" />}
                {submitButtonText}
                <Lottie
                  className="dark:invert"
                  play
                  animationData={arrowData}
                  style={{ width: 20, height: 20 }}
                />
              </Button>
            </PopoverTrigger>
            <PopoverContent
              ref={refPopover}
              side="top"
              className="w-fit border-0 p-0"
            >
              <Alert variant="destructive">
                <MdError />
                <AlertDescription>
                  {error?.message ?? 'Có lỗi xảy ra, vui lòng thử lại sau'}
                </AlertDescription>
              </Alert>
              <PopoverRadix.Arrow className="fill-accent" />
            </PopoverContent>
          </Popover>
        </form>
      </Form>
    </Card>
  )
}
